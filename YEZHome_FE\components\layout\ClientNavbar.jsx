"use client";

import {<PERSON>} from '@/i18n/navigation';
import {
  Heart,
  Menu,
  Wallet,
  RefreshCw,
  ChevronDown,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetHeader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import ButtonLogOut from "../ui/ButtonLogOut";
import { Button } from "../ui/button";
import { NavBarData } from "@/common/NavBarData";
import { memo, useState, useEffect, useCallback } from "react";
import { getFavoritesCount } from "@/app/actions/server/userFavorite";
import { LanguageSwitcher } from "@/components/ui/LanguageSwitcher";
import { useTranslations } from 'next-intl';
import NotificationBell from "./NotificationBell";
import { useAuth } from "@/contexts/AuthContext";
import { formatCurrency } from "@/lib/utils";

// Memoized components to prevent unnecessary re-renders
const UserMenu = memo(({isLoggedIn}) => {
    const t = useTranslations('Navbar');
    const { profile, loading, refreshProfile } = useAuth();

    console.log("render UserMenu login, profile, loading", isLoggedIn, profile, loading);

    // Early return if not logged in or no profile
    if (!isLoggedIn || !profile) return null;

    const walletInfo = profile?.user?.wallet;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center space-x-2 px-2 py-1 rounded-md bg-accent hover:bg-gray-200">
          <Avatar className="h-8 w-8">
            <AvatarImage src={profile.user?.avatarURL} alt={profile.user?.fullName} />
            <AvatarFallback>{profile.fullName?.substring(0, 2) || 'U'}</AvatarFallback>
          </Avatar>
          <span className="font-medium text-sm hidden sm:inline">{profile.user?.fullName}</span>
          <ChevronDown className="ml-auto h-4 w-4 text-gray-500" />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>{profile.user?.fullName}</DropdownMenuLabel>

          {/* Wallet Information */}
          {walletInfo && (
            <DropdownMenuItem className="flex items-center cursor-default">
              <Wallet className="mr-2 h-4 w-4 text-gray-500" />
              <span>{formatCurrency(walletInfo.balance)}</span>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuGroup>
            <DropdownMenuItem asChild>
              <Link href="/user/profile">
                {t('userMenuMyAccount')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/user/bds">
                {t('userMenuMyProperties')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/user/favorite">
                {t('userMenuMyFavorites')}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />

          {/* Refresh Button */}
          <DropdownMenuItem onClick={refreshProfile} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin text-coral-500' : ''}`} />
            <span>{t('refreshData') || 'Refresh Data'}</span>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem>
            <ButtonLogOut className="w-full text-left">
            </ButtonLogOut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
});
UserMenu.displayName = "UserMenu";

// This component might be removed if NavLinks in Navbar.jsx is translated
const MobileNavLinks = memo(() => {
  // Placeholder - Actual translation happens in Navbar.jsx
  return (
    <>
      {NavBarData.map((item) => (
        <Link
          key={item.id}
          href={item.url}
          className="text-lg text-navy-blue hover:text-coral-600 font-semibold"
        >
          {item.name}
        </Link>
      ))}
    </>
  )
});
MobileNavLinks.displayName = "MobileNavLinks";

const FavoriteIcon = memo(({ count }) => {
    const t = useTranslations('Navbar');
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link href="/user/favorite" className="relative">
              <Heart className="h-6 w-6 cursor-pointer hover:text-coral-600" />
              {count > 0 && (
                <span className="absolute -top-2 -right-2 bg-coral-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {count > 99 ? "99+" : count}
                </span>
              )}
            </Link>
          </TooltipTrigger>
          <TooltipContent>
            <p>{t('favoriteTooltip')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
});
FavoriteIcon.displayName = "FavoriteIcon";


export const ClientNavbar = memo(({ isLoggedIn }) => {
  const t = useTranslations('Navbar');
  const [favoriteCount, setFavoriteCount] = useState(0);

  const fetchFavoriteCount = useCallback(async () => {
    if (!isLoggedIn) return;
    try {
      const result = await getFavoritesCount();
      if (result.success && result.data) {
        setFavoriteCount(result.data.count);
      }
    } catch (error) {
      console.error("Error fetching favorite count:", error);
    }
  }, [isLoggedIn]);

  useEffect(() => {
    console.log("isLoggedId ClientNavbar", isLoggedIn);

    if (isLoggedIn) {
      fetchFavoriteCount();
    } else {
      setFavoriteCount(0);
    }

    const handleFavoritesChanged = () => {
      if (isLoggedIn) {
        fetchFavoriteCount();
      }
    };

    window.addEventListener("favorites-changed", handleFavoritesChanged);
    return () => {
      window.removeEventListener("favorites-changed", handleFavoritesChanged);
    };
  }, [isLoggedIn, fetchFavoriteCount]);

  console.log("render ClientNavbar", isLoggedIn);
  return (
    <>
      <div className="hidden md:flex space-x-4 pt-2">
        <div className="flex items-center space-x-4">
          {isLoggedIn ? (
            <>
              <NotificationBell />
              <FavoriteIcon count={favoriteCount} />
              <UserMenu isLoggedIn={isLoggedIn} />
              <Button asChild className="text-coral-500 bg-white border-coral border-2 hover:bg-coral-500 hover:text-white transition-all px-4 py-2 rounded-md">
                  <Link href="/user/bds/new">
                   {t('postProperty')}
                  </Link>
              </Button>
            </>
          ) : (
            <>
              <Link href="/tai-ung-dung" className="font-semibold hover:text-coral-600">
                {t('downloadApp')}
              </Link>
              <div className="h-6 w-px bg-gray-300 mx-2"></div>
              <Link href="/dang-nhap" className="font-semibold hover:text-coral-600">
                {t('login')}
              </Link>
              <div className="h-6 w-px bg-gray-300 mx-2"></div>
              <Link href="/dang-ki" className="font-semibold hover:text-coral-600">
                {t('register')}
              </Link>
               <Button asChild className="bg-white text-coral-500 border border-coral-500 hover:bg-coral-500 hover:text-white transition-all px-4 py-2 rounded-md font-semibold">
                  <Link href="/user/bds/new">
                   {t('postProperty')}
                  </Link>
              </Button>
            </>
          )}
          <LanguageSwitcher />
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden flex items-center gap-2"> 
        {!isLoggedIn && <FavoriteIcon count={favoriteCount} />}
         {isLoggedIn && <NotificationBell />} 
        <Sheet>
          <SheetTrigger asChild>
            <button className="text-navy-blue p-2">
              <Menu className="h-6 w-6" />
            </button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>{t('mobileMenuTitle')}</SheetTitle>
            </SheetHeader>
            <div className="flex flex-col space-y-4 mt-6">             
              <MobileNavLinks />
              {isLoggedIn ? (
                <>                  
                  <div className="py-2 border-b border-gray-200 mb-2">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/user.jpg" alt="user" />
                        <AvatarFallback>U</AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium">{t('userGreeting')}</span>
                        <span className="text-sm text-gray-500">User Account</span>
                      </div>
                    </div>
                  </div>
                  <Link
                    href="/user/profile"
                    className="text-lg text-navy-blue hover:text-coral-500 font-semibold"
                  >
                    {t('userMenuMyAccount')}
                  </Link>
                  <Link
                    href="/user/bds"
                    className="text-lg text-navy-blue hover:text-coral-500 font-semibold"
                  >
                    {t('userMenuMyProperties')}
                  </Link>
                  <Link
                    href="/user/favorite"
                    className="text-lg text-navy-blue hover:text-coral-500 font-semibold"
                  >
                    {t('userMenuMyFavorites')}
                  </Link>
                  <LanguageSwitcher />
                  <Button
                    size="lg"
                    className="bg-coral-500 text-white hover:bg-coral-600"
                    asChild
                  >
                    <Link href="/user/bds/new">{t('postProperty')}</Link>
                  </Button>
                  {/* ButtonLogOut needs translation */}
                  <ButtonLogOut></ButtonLogOut>
                </>
              ) : (
                <>
                  <LanguageSwitcher />
                  <Link
                    href="/dang-nhap"
                    className="text-lg text-coral-500 hover:text-coral-600 font-semibold"
                  >
                     {t('login')}
                  </Link>
                   <Link
                    href="/dang-ki"
                    className="text-lg text-navy-blue hover:text-coral-600 font-semibold"
                  >
                     {t('register')}
                  </Link>
                  <Button asChild size="lg" className="bg-coral-500 text-white hover:bg-coral-600">
                    <Link href="/user/bds/new">{t('postProperty')}</Link>
                  </Button>
                </>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
});
ClientNavbar.displayName = "ClientNavbar";

